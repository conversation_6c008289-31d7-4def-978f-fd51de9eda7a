import React from 'react';
import { Skill, SectionSettings } from '../../../types/cv.types';

interface SkillsPreviewProps {
  skills: Skill[];
  sectionSettings?: SectionSettings;
}

const SkillsPreview: React.FC<SkillsPreviewProps> = ({ skills, sectionSettings }) => {
  if (!skills.length) return null;

  const showProficiencyLevels = sectionSettings?.skills?.showProficiencyLevels ?? false;

  // Group skills by category
  const groupedSkills = skills.reduce((acc, skill) => {
    const category = skill.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(skill);
    return acc;
  }, {} as Record<string, Skill[]>);

  // Sort skills within each category by level if proficiency levels are shown
  Object.keys(groupedSkills).forEach(category => {
    if (showProficiencyLevels) {
      groupedSkills[category].sort((a, b) => (b.level || 0) - (a.level || 0));
    }
  });

  return (
    <div className="mb-6">
      <h2 className="text-lg font-bold text-gray-800 border-b border-gray-200 pb-1 mb-3">
        Skills
      </h2>

      <div className="space-y-4">
        {Object.entries(groupedSkills).map(([category, categorySkills]) => (
          <div key={category}>
            <h3 className="text-md font-semibold text-gray-700 mb-2">{category}</h3>
            <div className="grid grid-cols-2 gap-3">
              {categorySkills.map((skill) => (
                <div key={skill.id} className="flex items-center">
                  <span className="text-gray-700 mr-2">{skill.name}</span>
                  {showProficiencyLevels && skill.level && (
                    <div className="flex-1 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-600 rounded-full"
                        style={{ width: `${(skill.level / 5) * 100}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkillsPreview;