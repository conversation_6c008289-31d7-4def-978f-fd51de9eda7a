import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import { CVData, SectionVisibility } from '../types/cv.ts';
import { HtmlGenerator } from '../utils/htmlGenerator.ts';
import { createAppLogger } from '../utils/logger.ts';
import { BadRequestError, InternalServerError } from '../utils/errors.ts';

/**
 * PDF Service class following Single Responsibility Principle
 * Handles all PDF generation functionalities
 */
export class PdfService {
  private htmlGenerator: HtmlGenerator;
  private logger: ReturnType<typeof createAppLogger>;

  constructor() {
    this.htmlGenerator = new HtmlGenerator();
    this.logger = createAppLogger();
  }

  /**
   * Generate a CV PDF from data using Puppeteer
   * @param data - CV data
   * @param visibility - Visibility settings for sections
   * @param templateName - Template name to use (default: 'classic')
   * @returns PDF buffer as Uint8Array
   */
  public async generatePdf(data: CVData | null, visibility: SectionVisibility | null, templateName = 'classic-0'): Promise<Uint8Array> {
    if (!data || !visibility) {
      throw new BadRequestError('CV data and visibility settings are required');
    }

    let browser: Browser | null = null;
    try {
      // Generate HTML from CV data using template
      const html = await this.htmlGenerator.generateHTML(data, visibility, templateName);

      // Launch a headless browser with recommended security settings
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
        ],
      });

      // Create a new page
      const page = await browser.newPage();

      // Set the page content to our HTML
      await page.setContent(html, {
        waitUntil: 'networkidle0', // Wait until all resources are loaded
      });

      // Wait for fonts to load and content to render properly
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1000)));
      
      // Wait for any images to load
      await page.evaluate(() => {
        return Promise.all(Array.from(document.images, img => {
          if (img.complete) return Promise.resolve();
          return new Promise(resolve => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve);
          });
        }));
      });

      // Generate a PDF with optimized settings for consistency
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in',
        },
        displayHeaderFooter: false,
        preferCSSPageSize: false,
        scale: 0.8, // Slightly scale down to ensure content fits well
      });

      return pdfBuffer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Error generating PDF', { error: errorMessage, stack: error instanceof Error ? error.stack : undefined });
      // Throw a specific AppError for better handling downstream
      throw new InternalServerError(`Failed to generate PDF: ${errorMessage}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Get suggested filename for the generated PDF
   * @param data - CV data
   * @returns Suggested filename
   */
  public getSuggestedFilename(data: CVData | null): string {
    if (!data?.personalInfo) {
      return 'resume.pdf';
    }
    const { firstName, lastName } = data.personalInfo;
    const nameParts: string[] = [];
    if (firstName && firstName.trim()) {
      nameParts.push(firstName.trim());
    }
    if (lastName && lastName.trim()) {
      nameParts.push(lastName.trim());
    }

    if (nameParts.length === 0) {
      return 'resume.pdf';
    }

    return `${nameParts.join('_')}_Resume.pdf`;
  }

  /**
   * Get list of available templates
   * @returns Array of template names
   */
  public async getAvailableTemplates(): Promise<string[]> {
    return await this.htmlGenerator.getAvailableTemplates();
  }

  /**
   * Generate HTML from CV data (for preview)
   * @param data - CV data
   * @param visibility - Visibility settings for sections
   * @param templateName - Template name to use (default: 'classic')
   * @returns HTML string
   */
  public async generateHTML(data: CVData, visibility: SectionVisibility, templateName = 'classic-0'): Promise<string> {
    return await this.htmlGenerator.generateHTML(data, visibility, templateName);
  }

  /**
   * Generate PDF from pre-rendered HTML content (unified approach for all templates)
   * @param htmlContent - Pre-rendered HTML content
   * @param styles - CSS styles to include
   * @returns PDF buffer as Uint8Array
   */
  public async generatePdfFromHtml(htmlContent: string, styles: string = ''): Promise<Uint8Array> {
    let browser: Browser | null = null;
    try {
      // Create a complete HTML document with enhanced styles for PDF consistency
      const fullHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CV</title>
          <link rel="preconnect" href="https://fonts.googleapis.com">
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lora:wght@700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
          <script src="https://cdn.tailwindcss.com"></script>
          <style>
            ${styles}
            
            /* Enhanced PDF-specific styles for consistency */
            * {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            
            body { 
              margin: 0; 
              padding: 0; 
              background: #ffffff !important;
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            }
            
            /* Remove interactive elements for PDF */
            button, .hover\\:* { display: none !important; }
            
            /* Preserve gradients and colors in PDF */
            .bg-gradient-to-r {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            
            /* Ensure proper page sizing */
            .max-w-4xl, .max-w-800px { 
              max-width: 100% !important; 
              width: 100% !important; 
              margin: 0 auto !important;
            }
            
            /* Page break control */
            .page-break { page-break-before: always !important; }
            .no-break { page-break-inside: avoid !important; }
            
            /* Ensure consistent spacing */
            .space-y-4 > * + * { margin-top: 1rem !important; }
            .space-y-6 > * + * { margin-top: 1.5rem !important; }
            .space-y-8 > * + * { margin-top: 2rem !important; }
            
            /* Fix any layout issues in PDF */
            .overflow-hidden { overflow: visible !important; }
            
            /* Ensure text is readable */
            .text-white { color: #000000 !important; }
            .bg-gray-900 { background-color: #ffffff !important; color: #000000 !important; }
            
            @media print {
              /* Additional print-specific adjustments */
              .shadow-sm, .shadow-md, .shadow-lg, .shadow-xl, .shadow-2xl { 
                box-shadow: none !important; 
              }
              
              /* Ensure all content fits properly */
              * { 
                box-sizing: border-box !important; 
              }
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
        </html>
      `;

      // Launch a headless browser with recommended security settings
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
        ],
      });

      // Create a new page
      const page = await browser.newPage();

      // Set the page content to our HTML
      await page.setContent(fullHtml, {
        waitUntil: 'networkidle0', // Wait until all resources are loaded
      });

      // Wait for fonts to load and content to render properly
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1500)));
      
      // Wait for any images to load
      await page.evaluate(() => {
        return Promise.all(Array.from(document.images, img => {
          if (img.complete) return Promise.resolve();
          return new Promise(resolve => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve);
          });
        }));
      });

      // Generate a PDF with optimized settings for consistency
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in',
        },
        displayHeaderFooter: false,
        preferCSSPageSize: false,
        scale: 0.8, // Slightly scale down to ensure content fits well
      });

      return pdfBuffer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Error generating PDF from HTML', { error: errorMessage, stack: error instanceof Error ? error.stack : undefined });
      // Throw a specific AppError for better handling downstream
      throw new InternalServerError(`Failed to generate PDF from HTML: ${errorMessage}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
}
