<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{personalInfo.firstName}} {{personalInfo.lastName}} - Resume</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap');

      body {
        font-family: 'Plus Jakarta Sans', Arial, sans-serif;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        line-height: 1.5;
        scroll-behavior: smooth;
        overflow-y: auto;
      }

      .container-resume {
        font-size: 11pt;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        min-height: 100vh;
      }

      /* List styling for proper bullet points and numbered lists */
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
        margin: 0.5rem 0;
      }

      ol {
        list-style-type: decimal;
        padding-left: 1.5rem;
        margin: 0.5rem 0;
      }

      li {
        margin: 0.25rem 0;
        line-height: 1.5;
      }

      ul ul {
        list-style-type: circle;
      }

      ul ul ul {
        list-style-type: square;
      }

      .cv-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .section-title {
        color: #667eea;
        font-weight: 700;
        font-size: 18pt;
        margin-bottom: 12px;
        position: relative;
        padding-left: 20px;
      }

      .section-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2px;
      }

      .content-item {
        background: #f8fafc;
        margin-bottom: 10px;
        padding: 12px;
        border-radius: 12px;
        border-left: 4px solid #667eea;
      }

      .date-badge {
        background: #667eea;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 9pt;
        font-weight: 500;
        display: inline-block;
        margin-bottom: 4px;
      }

      .skill-tag {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 9pt;
        font-weight: 500;
        margin: 4px 8px 4px 0;
        display: inline-block;
      }

      @page {
        margin: 0;
        size: A4;
      }

      @media print {
        body {
          font-size: 10pt;
        }
        
        .container-resume {
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .section-title {
          font-size: 16pt;
        }
      }
    </style>
  </head>
  <body>
    <div class="container-resume">
      <div class="cv-content">
        <!-- Header -->
        {{#if personalInfo.firstName}}
          <div class="header-section">
            <h1 class="text-4xl font-bold mb-2">
              {{personalInfo.firstName}} {{personalInfo.lastName}}
            </h1>
            {{#if personalInfo.title}}
                            <h2 class="text-xl opacity-90 mb-3">{{personalInfo.title}}</h2>
            {{/if}}
            
            <div class="flex flex-wrap justify-center text-sm gap-4">
              {{#if personalInfo.email}}
                <div>📧 {{personalInfo.email}}</div>
              {{/if}}
              {{#if personalInfo.phone}}
                <div>📱 {{personalInfo.phone}}</div>
              {{/if}}
              {{#if personalInfo.address}}
                <div>📍 
                  {{personalInfo.address}}{{#if personalInfo.city}}, {{personalInfo.city}}{{/if}}{{#if personalInfo.state}}, {{personalInfo.state}}{{/if}}{{#if personalInfo.zipCode}} {{personalInfo.zipCode}}{{/if}}{{#if personalInfo.country}}, {{personalInfo.country}}{{/if}}
                </div>
              {{/if}}
              {{#if personalInfo.website}}
                <div>🌐 {{personalInfo.website}}</div>
              {{/if}}
              {{#if personalInfo.linkedin}}
                <div>💼 {{personalInfo.linkedin}}</div>
              {{/if}}
            </div>
          </div>
        {{/if}}

        <div class="p-8">
          <!-- Summary -->
          {{#if visibility.summary}}
            {{#if summary}}
                            <section class="mb-4">
                <h2 class="section-title">Professional Summary</h2>
                <div class="content-item">
                  <p class="text-sm leading-relaxed">{{summary}}</p>
                </div>
              </section>
            {{/if}}
          {{/if}}

          <!-- Work Experience -->
          {{#if visibility.workExperience}}
            {{#if (hasItems workExperience)}}
                            <section class="mb-4">
                <h2 class="section-title">Professional Experience</h2>
                {{#each workExperience}}
                  <div class="content-item">
                    <div class="date-badge">{{formatDate startDate}} - {{#if current}}Present{{else}}{{formatDate endDate}}{{/if}}</div>
                    <h3 class="font-bold text-lg mb-1">{{position}}</h3>
                    <div class="text-gray-600 font-medium mb-2">{{company}}{{#if location}} • {{location}}{{/if}}</div>
                    {{#if description}}
                      <div class="text-sm leading-relaxed">{{{description}}}</div>
                    {{/if}}
                  </div>
                {{/each}}
              </section>
            {{/if}}
          {{/if}}

          <!-- Projects -->
          {{#if visibility.projects}}
            {{#if (hasItems projects)}}
                            <section class="mb-4">
                <h2 class="section-title">Featured Projects</h2>
                {{#each projects}}
                  <div class="content-item">
                    <div class="date-badge">{{formatDate startDate}} - {{#if current}}Ongoing{{else}}{{formatDate endDate}}{{/if}}</div>
                    <h3 class="font-bold text-lg mb-1">
                      {{#if liveUrl}}
                        <a href="{{liveUrl}}" class="text-blue-600 underline">{{title}}</a>
                      {{else}}
                        {{title}}
                      {{/if}}
                    </h3>
                    {{#if description}}
                      <div class="text-sm leading-relaxed mb-3">{{{description}}}</div>
                    {{/if}}
                    {{#if (hasItems technologies)}}
                      <div class="mb-2">
                        {{#each technologies}}
                          <span class="skill-tag">{{.}}</span>
                        {{/each}}
                      </div>
                    {{/if}}
                    {{#if githubUrl}}
                      <div class="text-xs text-gray-600">
                        🔗 <a href="{{githubUrl}}" class="underline">{{githubUrl}}</a>
                      </div>
                    {{/if}}
                  </div>
                {{/each}}
              </section>
            {{/if}}
          {{/if}}

          <!-- Education -->
          {{#if visibility.education}}
            {{#if (hasItems education)}}
                            <section class="mb-4">
                <h2 class="section-title">Education</h2>
                {{#each education}}
                  <div class="content-item">
                    <div class="date-badge">{{formatDate startDate}} - {{#if current}}Present{{else}}{{formatDate endDate}}{{/if}}</div>
                    <h3 class="font-bold text-lg mb-1">{{institution}}</h3>
                    <div class="text-gray-600 font-medium mb-2">{{degree}}{{#if field}} in {{field}}{{/if}}{{#if location}} • {{location}}{{/if}}</div>
                    {{#if description}}
                      <div class="text-sm leading-relaxed">{{{description}}}</div>
                    {{/if}}
                  </div>
                {{/each}}
              </section>
            {{/if}}
          {{/if}}

          <!-- Skills -->
          {{#if visibility.skills}}
            {{#if (hasItems skills)}}
              <section class="mb-4">
                <h2 class="section-title">Core Skills</h2>
                {{#each (groupSkills skills)}}
                  <div class="content-item mb-3">
                    <h3 class="text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide">{{category}}</h3>
                    <div class="flex flex-wrap gap-1">
                      {{#each skills}}
                        <span class="skill-tag">
                          {{name}}{{#if ../../../sectionSettings.skills.showProficiencyLevels}}{{#if level}} ({{level}}/5){{/if}}{{/if}}
                        </span>
                      {{/each}}
                    </div>
                  </div>
                {{/each}}
              </section>
            {{/if}}
          {{/if}}

          <!-- Interests -->
          {{#if visibility.interests}}
            {{#if (hasItems interests)}}
                            <section class="mb-4">
                <h2 class="section-title">Interests</h2>
                <div class="content-item">
                  <p class="text-sm">{{join interests ", "}}</p>
                </div>
              </section>
            {{/if}}
          {{/if}}

          <!-- References -->
          {{#if visibility.references}}
            {{#if (hasItems references)}}
              <section>
                <h2 class="section-title">References</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {{#each references}}
                    <div class="content-item">
                      <h3 class="font-bold text-lg mb-1">{{name}}</h3>
                      <div class="text-gray-600 font-medium mb-2">{{position}}{{#if this.company}} at {{company}}{{/if}}</div>
                      {{#if email}}
                        <div class="text-sm">📧 {{email}}</div>
                      {{/if}}
                      {{#if phone}}
                        <div class="text-sm">📱 {{phone}}</div>
                      {{/if}}
                    </div>
                  {{/each}}
                </div>
              </section>
            {{/if}}
          {{/if}}
        </div>
      </div>
    </div>
  </body>
</html>